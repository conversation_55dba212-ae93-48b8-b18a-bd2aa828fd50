spring:
  application:
    name: graphql-crm

  threads:
    virtual:
      enabled: true

  graphql:
    graphiql:
      enabled: true

  datasource:
    url: ******************************************
    username: root
    password:
    driver-class-name: com.mysql.cj.jdbc.Driver

  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
    database-platform: org.hibernate.dialect.MySQLDialect
    properties:
      hibernate:
        format_sql: true